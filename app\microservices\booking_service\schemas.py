from pydantic import BaseModel, EmailStr
from typing import List, Optional, Any
from marshmallow_sqlalchemy import SQLAlchemySchema, auto_field
from .models import MasterBooking

class ProviderInfo(BaseModel):
    code: str

# PTCFare schema for detailed fare breakdown
class PTCFare(BaseModel):
    AddonDiscount: Optional[float] = 0
    AddonMarkup: Optional[float] = 0
    AgentMarkUp: Optional[float] = 0
    Ammendment: Optional[float] = 0
    API: Optional[float] = 0
    ATOAddonMarkup: Optional[float] = 0
    AtoCharge: Optional[float] = 0
    CGST: Optional[float] = 0
    CUTE: Optional[float] = 0
    Fare: float
    GrossFare: float
    IGST: Optional[float] = 0
    K3: Optional[float] = 0
    K7: Optional[float] = 0
    NetFare: float
    OfflineSeviceCharge: Optional[float] = 0
    OldSSRAmount: Optional[float] = 0
    OT: Optional[str] = ""
    OTT: Optional[str] = ""
    PHF: Optional[float] = 0
    PSF: Optional[float] = 0
    PTC: str
    RCF: Optional[float] = 0
    RCS: Optional[float] = 0
    ReissueCharge: Optional[float] = 0
    SGST: Optional[float] = 0
    ST: Optional[float] = 0
    Tax: Optional[float] = None
    TransactionFee: Optional[float] = 0
    UD: Optional[float] = 0
    VATonServiceCharge: Optional[float] = 0
    VATonTransactionFee: Optional[float] = 0
    YQ: Optional[float] = 0
    YR: Optional[float] = 0

class FlightDetails(BaseModel):
    FUID: str
    VAC: str
    MAC: str
    OAC: str
    Airline: str
    FlightNo: str
    ArrivalTime: str
    DepartureTime: str
    ArrivalCode: str
    DepartureCode: str
    Duration: str
    FareBasisCode: Optional[str] = ""
    ArrAirportName: str
    DepAirportName: str
    RBD: Optional[str] = ""
    Cabin: str
    Refundable: str
    # Additional fields from YAML
    AirlineName: Optional[str] = None
    AirCraft: Optional[str] = None
    Amenities: Optional[Any] = None
    ArrivalTerminal: Optional[str] = None
    DepartureTerminal: Optional[str] = None
    CarbonEmissions: Optional[float] = 0
    EquipmentType: Optional[str] = ""
    FareClass: Optional[str] = None
    Farelink: Optional[str] = None
    FBC: Optional[str] = None
    FCBegin: Optional[str] = ""
    FCEnd: Optional[str] = ""
    Provider: Optional[str] = None
    Hops: Optional[Any] = None
    Seats: Optional[int] = None

class FareDetails(BaseModel):
    GrossFare: float
    NetFare: float
    OldSSRAmount: Optional[float] = 0
    PTCFare: Optional[List["PTCFare"]] = []
    TotalBaseFare: Optional[float] = 0
    TotalTax: Optional[float] = 0
    TotalAddonDiscount: Optional[float] = 0
    TotalAddonMarkup: Optional[float] = 0
    TotalAgentMarkUp: Optional[float] = 0
    TotalAtoCharge: Optional[float] = 0
    TotalCommission: Optional[float] = 0
    TotalReissueCharge: Optional[float] = 0
    TotalServiceTax: Optional[float] = 0
    TotalTransactionFee: Optional[float] = 0
    TotalVATonServiceCharge: Optional[float] = 0
    TotalVATonTransactionFee: Optional[float] = 0

# SSR (Special Service Request) schema
class SSR(BaseModel):
    Code: str
    Description: str
    PieceDescription: Optional[str] = ""
    Charge: float
    OrginalCharge: Optional[float] = 0
    VAT: Optional[float] = 0
    Type: str
    Category: Optional[str] = ""
    PTC: str
    ID: int
    IsFreeMeal: Optional[bool] = True
    MealImage: Optional[str] = ""
    SSRUrl: Optional[str] = None
    OriginID: Optional[int] = 0
    OriginCharge: Optional[float] = 0
    AdditionalField: Optional[List[Any]] = []

# Rules schema for fare rules
class RuleInfo(BaseModel):
    AdultAmount: str
    ChildAmount: str
    InfantAmount: str
    YouthAmount: Optional[str] = None
    Description: str
    CurrencyCode: Optional[str] = ""
    TimeDay: Optional[str] = None
    RuleText: Optional[str] = ""

class Rule(BaseModel):
    Info: List[RuleInfo]
    Head: str

class FareRule(BaseModel):
    OrginDestination: str
    FareRuleText: Optional[str] = None
    Rule: List[Rule]
    FUID: Optional[str] = None
    Provider: Optional[str] = None
    SpecialInformations: Optional[str] = ""

class Segment(BaseModel):
    FUID: Optional[str] = None
    VAC: Optional[str] = None
    Flight: FlightDetails
    Fares: FareDetails
    SSR: Optional[List["SSR"]] = []
    Rules: Optional[List[FareRule]] = []

class Journey(BaseModel):
    ChannelCode: Optional[str] = ""
    Duration: Optional[str] = ""
    FCType: Optional[str] = ""
    GrossFare: float
    NetFare: float
    Notices: Optional[List[Any]] = []
    OrderID: int
    Promo: Optional[Any] = None
    Provider: str
    SeatHold: Optional[bool] = False
    Segments: List[Segment]
    Stops: int
    # Additional fields from YAML
    Offer: Optional[str] = ""
    CSBalance: Optional[float] = 0

class Trip(BaseModel):
    From: Optional[str] = None
    To: Optional[str] = None
    TUI: Optional[str] = None
    Journey: List[Journey]

class Traveller(BaseModel):
    ID: int
    PaxID: int
    Operation: Optional[str] = ""
    Title: str
    FName: str
    LName: str
    Age: int
    DOB: str
    Country: Optional[str] = ""
    Gender: str
    PTC: str
    Nationality: str
    PassportNo: str
    PLI: str
    PDOE: str
    VisaType: Optional[str] = None
    DefenceID: Optional[str] = ""
    PaxCategoryID: Optional[str] = ""
    DocType: str
    # Additional fields from YAML
    DOBDay: Optional[str] = "0"
    DOBMonth: Optional[str] = "0"
    DOBYear: Optional[str] = "0"
    PDOEDay: Optional[str] = "0"
    PDOEMonth: Optional[str] = "0"
    PDOEBYear: Optional[str] = "0"

class ContactInfo(BaseModel):
    Title: Optional[str] = ""
    FName: Optional[str] = ""
    LName: Optional[str] = ""
    Mobile: str
    Phone: Optional[str] = ""
    Email: str
    Address: str
    CountryCode: str
    MobileCountryCode: str
    State: str
    City: str
    PIN: str
    GSTAddress: Optional[str] = ""
    GSTCompanyName: Optional[str] = ""
    GSTTIN: Optional[str] = ""
    UpdateProfile: Optional[bool] = False
    IsGuest: Optional[bool] = True
    SaveGST: Optional[bool] = False
    Language: Optional[str] = ""
    GSTaddress: Optional[str] = ""

# CrossSell schema
class CrossSell(BaseModel):
    Code: str
    TransactionID: int

# Auxiliaries schema
class Auxiliary(BaseModel):
    Code: Optional[str] = None
    EmployeeID: Optional[str] = None
    Amount: float

class FlightBooking(BaseModel):
    provider_info: ProviderInfo
    TUI: str
    Mode: Optional[str] = None
    TransactionID: Optional[int] = None
    ADT: int
    CHD: int
    INF: int
    NetAmount: float
    AirlineNetFare: Optional[float] = None
    SSRAmount: Optional[float] = 0
    CrossSellAmount: Optional[float] = 0
    GrossAmount: float
    Trips: List[Trip]
    Rules: Optional[List[FareRule]] = []
    SSR: Optional[List["SSR"]] = []
    CrossSell: Optional[List["CrossSell"]] = []
    Auxiliaries: Optional[List["Auxiliary"]] = []
    Hold: bool
    ActualHoldTime: int
    ActualDisplayTime: int
    CeilingInfo: Optional[str] = ""

# Main booking request schema that matches YAML exactly
class BookingRequest(BaseModel):
    Code: Optional[str] = "200"
    Msg: Optional[List[str]] = ["Success"]
    From: Optional[str] = None
    To: Optional[str] = None
    FromName: Optional[str] = None
    ToName: Optional[str] = None
    OnwardDate: Optional[str] = None
    ADT: Optional[int] = None
    CHD: Optional[int] = None
    INF: Optional[int] = None
    NetAmount: Optional[float] = None
    GrossAmount: Optional[float] = None
    FareType: Optional[str] = None
    Trips: Optional[List[Trip]] = []
    TUI: Optional[str] = None
    CustomTripBooking: Optional[int] = None
    travellers: Optional[List[Any]] = []  # Using Any for flexibility with different traveller formats
    # Alternative structure for different booking formats
    flight_booking: Optional[FlightBooking] = None
    Travellers: Optional[List[Traveller]] = []
    ContactInfo: Optional[ContactInfo] = None


class MasterBookingSchema(SQLAlchemySchema):
    class Meta:
        model = MasterBooking
        load_instance = True

    id = auto_field()
    booking_reference = auto_field()
    service_type = auto_field()
    status = auto_field()
    payment_status = auto_field()
    # Excludes created_at and updated_at