from pydantic import BaseModel, Field, EmailStr, validator
from typing import List, Optional, Dict
from marshmallow_sqlalchemy import SQLAlchemySchema, auto_field
from datetime import datetime
from .models import MasterBooking

class ProviderInfo(BaseModel):
    code: str

class FlightDetails(BaseModel):
    FUID: str
    VAC: str
    MAC: str
    OAC: str
    Airline: str
    FlightNo: str
    ArrivalTime: str
    DepartureTime: str
    ArrivalCode: str
    DepartureCode: str
    Duration: str
    FareBasisCode: str
    ArrAirportName: str
    DepAirportName: str
    RBD: str
    Cabin: str
    Refundable: str

class FareDetails(BaseModel):
    # PTC: str
    # Fare: float
    # YQ: float
    # PSF: float
    GrossFare: float
    NetFare: float

class Segment(BaseModel):
    Flight: FlightDetails
    Fares: FareDetails

class Journey(BaseModel):
    Provider: str
    Stops: int
    Segments: List[Segment]
    Offer: str
    OrderID: int
    GrossFare: float
    NetFare: float

class Trip(BaseModel):
    Journey: List[Journey]

class Traveller(BaseModel):
    ID: int
    PaxID: int
    Title: str
    FName: str
    LName: str
    Age: int
    DOB: str
    Gender: str
    PTC: str
    PLI: str
    PDOE: str
    Nationality: str
    PassportNo: str
    VisaType: Optional[str]
    DocType: str

class ContactInfo(BaseModel):
    Title: Optional[str]
    FName: str
    LName: str
    Mobile: str
    Phone: Optional[str]
    Email: EmailStr
    Address: Optional[str]
    CountryCode: str
    MobileCountryCode: Optional[str]
    State: Optional[str]
    City: Optional[str]
    PIN: Optional[str]
    GSTAddress: Optional[str]
    GSTCompanyName: Optional[str]
    GSTTIN: Optional[str]
    UpdateProfile: Optional[bool]
    IsGuest: Optional[bool]
    SaveGST: Optional[bool]
    Language: Optional[str]

class FlightBooking(BaseModel):
    provider_info: ProviderInfo
    TUI: str
    ADT: int
    CHD: int
    INF: int
    NetAmount: float
    Trips: List[Trip]
    AirlineNetFare: float
    SSRAmount: float
    CrossSellAmount: float    
    GrossAmount: float
    Hold: bool
    ActualHoldTime: int
    ActualDisplayTime: int

class BookingRequest(BaseModel):
    flight_booking: FlightBooking
    Travellers: List[Traveller]
    ContactInfo: ContactInfo


class MasterBookingSchema(SQLAlchemySchema):
    class Meta:
        model = MasterBooking
        load_instance = True

    id = auto_field()
    booking_reference = auto_field()
    service_type = auto_field()
    status = auto_field()
    payment_status = auto_field()
    # Excludes created_at and updated_at