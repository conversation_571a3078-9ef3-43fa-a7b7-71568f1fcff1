def flight_search_translate_client_to_provider(client_request):
    """
    Translates a client flight search request into a provider-specific format.

    Args:
        client_request (dict): The client's flight search request containing
                               cabin class, passenger information, trip details,
                               and optional return date.

    Returns:
        dict: The formatted request to be sent to the flight provider.
    """

    # Mapping of cabin class codes from client input to provider format
    cabin_class_mapping = {
        "E": "ECONOMY",           # Map economy class code
        "B": "BUSINESS",          # Map business class code
        "PE": "PREMIUM_ECONOMY",  # Map premium economy class code
        "F": "FIRST"              # Map first class code
    }

    # Build route infos - only include routes with valid travel dates
    route_infos = []

    for trip in client_request.get("Trips", []):
        # Only add route if it has a valid travel date
        if trip.get("OnwardDate"):
            route_infos.append({
                "fromCityOrAirport": {
                    "code": trip.get("From")
                },
                "toCityOrAirport": {
                    "code": trip.get("To")
                },
                "travelDate": trip.get("OnwardDate")
            })

    # Create the provider request dictionary
    provider_request = {
        "searchQuery": {  # The main search query for the provider
            "cabinClass": cabin_class_mapping.get(client_request.get("Cabin"), "ECONOMY"),  # Get cabin class or default to ECONOMY
            "paxInfo": {  # Passenger information section
                "ADULT": str(client_request.get("ADT", 1)),  # Number of adults as a string
                "CHILD": str(client_request.get("CHD", 0)),  # Number of children as a string
                "INFANT": str(client_request.get("INF", 0))   # Number of infants as a string
            },
            "routeInfos": route_infos,  # Only valid routes with travel dates
            "searchModifiers": {},  # Empty search modifiers
            "preferredAirline": []  # Empty preferred airlines list
        }
    }

    # Check if the request includes a return date for round trip
    if (len(client_request.get("Trips", [])) == 1 and
        "ReturnDate" in client_request["Trips"][0] and
        client_request["Trips"][0]["ReturnDate"] is not None and
        client_request["Trips"][0]["ReturnDate"].strip() != ""):

        # Add return trip to the route info
        provider_request["searchQuery"]["routeInfos"].append({
            "fromCityOrAirport": {
                "code": client_request["Trips"][0]["To"]  # Arrival code becomes departure for return
            },
            "toCityOrAirport": {
                "code": client_request["Trips"][0]["From"]  # Departure code becomes arrival for return
            },
            "travelDate": client_request["Trips"][0]["ReturnDate"]  # Set the return travel date
        })

    return provider_request  # Return the formatted request for the provider